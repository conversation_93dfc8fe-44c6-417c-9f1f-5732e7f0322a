# FastAPI 智能问答缓存与代理服务

这是一个基于FastAPI的高级聊天服务，它不仅代理到第三方聊天API（如FastGPT），还集成了一个智能的本地问答缓存系统，以提高响应速度、降低API成本，并能通过大模型动态处理与核心业务无关的问题。

## 核心功能

- **🚀 高性能异步框架**: 基于 `FastAPI` 和 `httpx`，提供全异步IO处理能力。
- **🧠 智能问答缓存**: 
  - 使用 `ChromaDB` 作为向量数据库，实现高效的语义检索。
  - 结合 `sentence-transformers` 重排模型，提高相似度匹配的精准度。
  - 缓存命中时，使用 `Ollama` 本地大模型对答案进行二次总结，使其更贴合用户当前问题。
- **🤖 Ollama 集成**: 
  - 深度集成 `Ollama`，用于文本嵌入、答案总结、问题分类和智能回复生成。
  - 支持自定义本地部署的大语言模型（如 `deepseek-v2`, `llama3.1` 等）。
- **🧭 动态对话流程**:
  - **缓存优先**: 优先在本地知识库中检索答案。
  - **智能分类**: 缓存未命中时，使用大模型判断问题是否与核心业务（MDM）相关。
  - **逻辑分流**: 
    - **相关问题**: 代理到 `FastGPT` 获取权威答案，并自动将新答案存入缓存。
    - **无关问题**: 调用大模型生成礼貌的拒绝与引导消息，避免无效的API请求。
- **🔐 安全与稳定**:
  - 提供基于 `X-API-Key` 的API密钥认证。
  - 内置 `slowapi` 实现基础的接口限流。

## 对话处理流程

下图详细展示了 `/chat/fastgpt` 接口处理用户请求的完整逻辑：

```mermaid
graph TD
    A["开始: 用户向 /chat/fastgpt 发送问题"] --> B{"知识库检索"};
    B --> C{"缓存命中?<br/>(相似度 >= 0.95)"};

    C -- "是" --> D["使用Ollama大模型总结缓存答案<br/>(流式输出)"];
    D --> Z["结束"];
    
    C -- "否" --> G["使用大模型进行问题分类<br/>(输出JSON)"];
    G --> H{"与MDM相关?"};
    
    H -- "否" --> I["使用大模型生成并流式输出<br/>礼貌的拒绝消息"];
    I --> Z;
    
    H -- "是" --> K["调用外部FastGPT接口<br/>(流式)"];
    K -- "异步任务" --> L["将新的问答对<br/>存入知识库"];
    K -- "实时数据流" --> M["将FastGPT的回复<br/>流式传输给用户"];
    M --> Z;
```

## 环境与配置

### 1. Ollama 服务
确保你的 `Ollama` 服务正在运行，并且已经下载了项目所需的模型。

- **检查与拉取模型**:
  ```bash
  # 查看已下载模型
  ollama list

  # 拉取本项目所需的模型
  ollama pull bge-m3:latest
  ollama pull deepseek-v2:latest
  ```

### 2. Python 依赖
```bash
pip install -r requirements.txt
```

### 3. 服务配置
你可以在 `cache_service.py` 文件中修改核心配置：

- `OLLAMA_HOST`: 你的Ollama服务器地址。
- `OLLAMA_EMBED_MODEL`: 用于文本向量化的嵌入模型。
- `OLLAMA_LLM_MODEL`: 用于答案总结和分类的大语言模型。
- `SIMILARITY_THRESHOLD`: 缓存命中的相似度阈值（建议 `0.9` 到 `0.95`）。

## 本地运行

```bash
# --reload 会在代码变更后自动重启服务
uvicorn main:app --reload --port 9527
```

## API 端点

### 主要接口

- `POST /mdm_chat/chat/fastgpt`
  - **功能**: 核心对话接口，实现了上述完整的动态处理流程。
  - **认证**: 需要在请求头中提供 `X-API-Key`。
  - **请求体**: (FastGPT兼容格式)
    ```json
    {
      "messages": [
        {
          "role": "user",
          "content": "你的问题"
        }
      ]
    }
    ```
  - **响应**: SSE (Server-Sent Events) 流式响应。

### 辅助接口

- `GET /mdm_chat/cache/dashboard`: 查看当前缓存中所有问答对的仪表盘页面。
- `POST /mdm_chat/cache/clear`: 清空所有缓存。
- `POST /mdm_chat/cache/delete/{item_id}`: 删除指定ID的缓存条目。

## Docker 部署

你可以使用 `docker-compose` 来方便地部署整个服务。

1. **授权部署脚本**:
   ```bash
   chmod +x deploy.sh
   ```

2. **执行部署**:
   ```bash
   ./deploy.sh
   ```
   该脚本会自动创建所需的 `logs` 目录，并构建、启动服务。

3. **管理服务**:
   ```bash
   # 查看服务状态
   docker-compose ps

   # 停止服务
   docker-compose down
   ``` 