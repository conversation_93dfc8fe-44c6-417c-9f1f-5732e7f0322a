from datetime import datetime, timedelta
from typing import Dict, Optional, Any

class TokenCache:
    def __init__(self):
        self._cache: Dict[str, Dict] = {}
    
    def set_token(self, username: str, token_data: Dict[str, Any], expires_in: int = 86400) -> None:
        """
        存储token数据，默认过期时间为1天（86400秒）
        """
        self._cache[username] = {
            "token_data": token_data,
            "expires_at": datetime.now() + timedelta(seconds=expires_in)
        }
    
    def get_token(self, username: str) -> Optional[Dict[str, Any]]:
        """
        获取token数据，如果过期则返回None
        """
        if username not in self._cache:
            return None
            
        token_data = self._cache[username]
        if datetime.now() > token_data["expires_at"]:
            del self._cache[username]
            return None
            
        return token_data["token_data"]
    
    def remove_token(self, username: str) -> None:
        """
        删除token
        """
        if username in self._cache:
            del self._cache[username]

# 创建全局缓存实例
token_cache = TokenCache() 


