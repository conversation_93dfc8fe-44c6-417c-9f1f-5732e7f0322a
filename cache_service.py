import ollama
import chromadb
from typing import List, Dict, Any, Optional, AsyncGenerator
import uuid
import numpy as np
from sentence_transformers import CrossEncoder



# --- 配置 ---
OLLAMA_HOST = "http://**************:11434"  # 在这里配置您的Ollama服务器地址
OLLAMA_EMBED_MODEL = 'bge-m3:latest'
# OLLAMA_LLM_MODEL = 'deepseek-r1:70b'  # 您可以换成 llama3, gemma 等
OLLAMA_LLM_MODEL = 'deepseek-v2:latest'
CHROMA_PATH = "./chroma_db"
COLLECTION_NAME = "qa_cache"
SIMILARITY_THRESHOLD = 0.95 
RERANKER_MODEL = 'BAAI/bge-reranker-base'

class QACacheService:
    def __init__(self):
        """初始化服务，连接到Ollama和ChromaDB，并加载重排模型"""
        print("正在初始化问答缓存服务...")
        try:
            self.ollama_client = ollama.AsyncClient(host=OLLAMA_HOST)
            # 在异步上下文中，我们不能在__init__中运行检查
            # 将在首次API调用时或通过一个专门的启动事件来处理
        except Exception as e:
            print(f"错误：Ollama 客户端初始化失败。 {e}")
            raise

        try:
            self.chroma_client = chromadb.PersistentClient(path=CHROMA_PATH)
            # 强制使用余弦相似度，这对于文本语义更有效
            self.collection = self.chroma_client.get_or_create_collection(
                name=COLLECTION_NAME,
                metadata={"hnsw:space": "cosine"}
            )
            print("ChromaDB 连接成功 (使用余弦相似度)。")
        except Exception as e:
            print(f"错误：无法初始化 ChromaDB。 {e}")
            raise

        try:
            print(f"正在加载重排模型: {RERANKER_MODEL} (首次加载需要下载)...")
            self.reranker = CrossEncoder(RERANKER_MODEL)
            print("重排模型加载成功。")
        except Exception as e:
            print(f"错误：无法加载重排模型。 {e}")
            raise
        
        print("问答缓存服务初始化完成。")

    async def _check_ollama_models(self):
        """检查所需的Ollama模型是否已下载，并列出可用模型"""
        print("正在检查Ollama模型...")
        available_models_info = await self.ollama_client.list()
        models = [model['name'] for model in available_models_info['models']]

        # 打印所有可用模型
        if models:
            print("Ollama 服务器上可用的模型:")
            for model_info in available_models_info['models']:
                size_gb = model_info.get('size', 0) / (1024**3)
                print(f"  - {model_info['name']} (Size: {size_gb:.2f} GB)")
        else:
            print("警告：Ollama 服务器上没有找到任何模型。")

        required_models = [OLLAMA_EMBED_MODEL, OLLAMA_LLM_MODEL]
        for model in required_models:
            if model not in models and f"{model}:latest" not in models:
                print(f"警告：代码中配置的模型 '{model}' 未在服务器上找到。")
                print(f"请在Ollama服务器上运行 'ollama pull {model}' 来下载它，或修改本地配置。")

    async def _get_embedding(self, text: str) -> List[float]:
        """为单个文本生成向量，并进行归一化处理"""
        # 1. 从Ollama获取原始向量
        result = await self.ollama_client.embeddings(model=OLLAMA_EMBED_MODEL, prompt=text)
        original_embedding = result['embedding']
        
        # 2. 使用Numpy进行归一化
        norm = np.linalg.norm(original_embedding)
        if norm == 0:
            return original_embedding # 避免除以零
        
        normalized_embedding = original_embedding / norm
        
        # 3. 返回归一化后的向量 (以Python列表形式)
        return normalized_embedding.tolist()

    async def search_similar_question(self, question: str) -> Optional[Dict[str, Any]]:
        """
        在缓存中进行两步式搜索：检索(Retrieve) + 重排(Rerank)。
        """
        print(f"正在为问题执行两步式搜索: {question}")
        
        # --- 步骤 1: 检索 (从ChromaDB获取Top 5候选) ---
        question_embedding = await self._get_embedding(question)
        results = self.collection.query(
            query_embeddings=[question_embedding],
            n_results=5 # 获取5个最相似的候选
        )
        
        if not results['ids'][0]:
            print("检索阶段：数据库中没有内容。")
            return None

        # --- 步骤 2: 重排 (使用CrossEncoder计算精确相关性) ---
        candidate_questions = [meta.get("original_question", "") for meta in results['metadatas'][0]]
        
        # 创建 [查询, 候选] 对
        rerank_pairs = [[question, cand_q] for cand_q in candidate_questions]
        
        if not rerank_pairs:
            print("重排阶段：没有有效的候选对。")
            return None

        print(f"重排阶段：正在为 {len(rerank_pairs)} 个候选计算精确分数...")
        scores = self.reranker.predict(rerank_pairs)
        
        # 找到分数最高的候选
        best_index = np.argmax(scores)
        best_score = scores[best_index]
        
        print(f"找到最相关的问题，重排分数: {best_score:.4f}")

        # 将原始数据和重排分数都返回
        best_candidate_metadata = results['metadatas'][0][best_index]
        return {
            "question": best_candidate_metadata.get("original_question"),
            "answer": best_candidate_metadata.get("original_answer"),
            "similarity": best_score # 现在这个字段代表更精确的"重排分数"
        }

    async def add_qa_pair(self, question: str, answer: str):
        """将一个新的问答对添加到缓存中"""
        print("正在将新的问答对添加到缓存...")
        question_embedding = await self._get_embedding(question)
        
        self.collection.add(
            embeddings=[question_embedding],
            metadatas=[{"original_question": question, "original_answer": answer}],
            ids=[str(uuid.uuid4())]
        )
        print("添加成功。")

    async def summarize_answer_with_ollama(self, new_question: str, cached_qa: Dict[str, Any]) -> AsyncGenerator:
        """
        使用Ollama LLM对缓存的答案进行总结，以更好地回答新问题。
        返回一个生成器，用于流式传输总结后的答案。
        """
        print("正在使用Ollama对缓存答案进行总结...")
        
        # 使用用户提供的专业、结构化的提示词模板
        prompt = f"""
# 角色
你是一位专业且高效的 Easycontrol MDM 产品智能客服，精通产品功能相关知识，始终以友好、耐心的态度解答用户提出的各类问题。

## 技能
### 技能 1: 精准理解与回复问题
1. 深入、全面分析用户提出的新问题。
2. 仔细研究以下背景知识中给出的答案，然后用自然流畅的语言直接回答新问题。回复的语言类型应与用户提问保持一致。
3. 你的回复必须显得自然、自信，避免暴露出你是基于背景知识进行回答的痕迹。

### 技能 2: 处理模糊问题
1. 当用户提出的问题模糊或不明确时，通过友好询问来进一步明确用户的意图。
2. 例如，可以询问用户具体想了解 Easycontrol MDM产品的哪方面功能。

## 问题处理范围
以下问题属于 Easycontrol MDM 产品功能范围，应当回答：

### 设备注册与管理
- **安卓注册**: MDM注册、ROM注册、Android Enterprise、EMM零接触注册、QR码注册、PIN码注册、NFC注册、ADB注册、BYOD自注册
- **iOS注册**: APNs证书管理、苹果注册(ABM)、Apple配置程序、认证注册
- **macOS注册**: APNs证书、苹果注册(ABM)、Apple配置程序、账号驱动注册
- **Linux注册**: CLI注册

### 设备管理
- **设备列表**: 添加设备、批量设备、更换设备组、删除设备、导出、精确搜索、设备统计、同步数据、应用策略
- **设备组**: 设备信息管理、软件信息、硬件信息、设备性能、无线信息、WiFi信息、蓝牙信息、策略详情、配置告警、网络信息、显示信息、交互设备信息
- **设备操作**: 关机、重启、锁定、解锁、解绑设备、发送信息、跑马灯、数据同步、屏幕截图、设置密码、清除密码、恢复出厂设置、设置音量、设置屏幕亮度、定位、远程协助、重命名设备、TCPing设备、自定义表单、清除应用缓存、导出设备、安装应用、卸载应用、强制响铃、关联用户、设备位置追踪、删除设备、添加标签、批量添加标签、启用激活锁、清除激活锁、系统升级、擦除设备、同步用户账户、创建用户账户、迁移至设备组、设置过期时间、显示延期标识、导出设备详情、应用策略

### 应用管理
- **应用商店**: 添加应用、编辑应用、删除应用、推送应用、推送至设备、下载应用、同步Google应用、自定义表单、Google Play Store、同步Apple商店应用、静默安装、静默卸载、推送策略
- **应用策略**: 静默安装、静默卸载、清理缓存、移除、禁用应用、禁用相机、更新、应用权限管理
- **系统应用**: 应用放行、同步系统应用、桌面显示、网络开放
- **OEMConfig**: 新增配置、更新配置、删除配置、更改组
- **应用组**: 初始化更新、静默安装、静默卸载、更新、更新通知、禁用应用、屏蔽通知、禁止卸载、清除缓存、移除、时间围栏、添加时间围栏、应用保活

### OTA升级
- **快速升级**: 升级版本管理、升级计划、升级分析、自定义表单、测试通过、测试失败、发布、取消发布、升级包、升级策略、目标版本、禁用/启用、升级报告、删除、导出

### 策略管理
- **密码策略**: 密码设置
- **通讯与网络**: 禁止拨打电话、禁止收发短信、禁用移动数据、禁用热点、禁用蓝牙、禁用WiFi、禁用NFC、强制打开WiFi、强制打开数据流量、锁定至4G频段、禁用VPN、禁止配置VPN
- **安全性**: USB仅充电、禁用开发者模式、禁用USB调试、禁用NFC共享、禁用外部存储、禁用安全模式、禁止未知来源、离线保护、USB数据访问、开发者模式、FRP
- **功能性**: 禁用恢复出厂设置、禁用相机、禁用通知栏、禁止应用发送通知、禁止安装应用、禁止卸载、禁止壁纸更改、禁用麦克风、禁用飞行模式、禁止添加用户、Play Store模式、禁用状态栏、禁用截屏、禁用手动截屏、禁用定位服务、禁用闪光灯、禁用系统升级、禁用更改系统日期和时间设置、禁用通过电源键进入关机界面、禁用音量键、禁用关机菜单、禁止亮屏下按电源键休眠、禁用多窗口、禁用搜索、导航栏隐藏、设置系统时间、默认桌面、始终连接数据业务、禁用USB大容量存储、禁用USB文件传输、禁用设备音量设置、禁用屏幕亮度设置、应用自动更新
- **锁屏配置**: 锁屏配置
- **时间与日期**: 时间与日期设置
- **KIOSK模式**: KIOSK模式、状态栏、电源键、导航栏、设备设置、应用包名、锁定任务白名单
- **定时开关机/重启**: 定时开机、定时关机、定时重启
- **定制化设置**: 移动数据、WLAN、关于、桌面/锁屏壁纸、开关机动画、Chrome黑白名单、APP黑白名单、URL黑名单、应用权限管理、私人DNS、隐藏应用、APN、Web应用、VPN、Exchange ActiveSync、定制设置、WiFi配置、华为浏览器黑白名单、蓝牙黑白名单、VPN配置、Email、Email设置、Exchange ActiveSync

### 位置管理
- **位置管理**: 位置管理功能
- **设备重定位**: 添加设备重定位、编辑设备重定位、删除设备重定位、禁用/启用设备重定位

### 告警管理
- **告警操作**: 添加告警、编辑告警、删除告警、禁用/启用
- **告警类型**: 电量百分比、可用ROM、可用RAM、时间围栏、地理围栏、设备完整性、SIM卡插入/移除、数据使用、电池健康、设备离线、交互设备、渠道围栏
- **告警日志**: 告警日志

### 文件管理
- **源文件**: 添加文件、更新文件、推送文件、下载、删除
- **文件同步**: 添加规则、编辑规则、禁用/启用规则、删除规则
- **文件库**: 下载、删除

### 系统管理
- **用户管理**: 添加用户、编辑用户、删除用户、重置密码、禁用/启用、多重身份验证
- **角色管理**: 添加角色、编辑角色、删除角色、功能权限
- **日志管理**: 服务端日志、设备端日志
- **跑马灯**: 添加跑马灯、复制、编辑跑马灯、删除、禁用/启用
- **证书管理**: 添加证书、修改证书、删除证书
- **用户管理**: 设备许可数、多重身份验证、隐藏云服务入口
- **Microsoft Entra ID**: 配置、同步、删除

### 仪表盘
- **设备活跃度统计**: 设备组、最后连接时间、MDM版本、版本分布、设备统计

### 其他MDM相关功能
- 任何涉及设备管理、应用管理、策略配置、安全管控、远程操作、数据同步、用户管理、系统配置等MDM核心功能的问题

## 限制:
- 你的回答就是最终要呈现给用户的答案，绝不能包含任何形式的思考过程、内心独白或元注释（如 "[思考]..."）。
- 你的回答不得包含"根据背景知识..."、"从缓存中找到..."等任何提及信息来源的字眼。
- 回答不得包含以下内容：
  (1) 敏感数据（如 Easycontrol 销售，财务数据）。
  (2) 个人信息（例如姓名，电话号码，地址）。
  (3) 非法、不道德、政治敏感的主题。
  (4) 未来或不确定因素的预测。
  (5) 代码示例。
- 回复应简洁明了，重点突出，字数一般不超过 300 字。  
- 仅围绕 Easycontrol MDM 产品功能相关问题进行回答，拒绝回答与该产品功能无关的话题。

---
[背景知识]
旧问题: {cached_qa['question']}
旧答案: {cached_qa['answer']}
---
[任务]
请严格遵守以上所有规则，直接针对以下新问题，生成一个最终、完整的回答。

新问题: "{new_question}"
最终回答:
"""
        
        stream = await self.ollama_client.generate(
            model=OLLAMA_LLM_MODEL,
            prompt=prompt,
            stream=True
        )
        
        async for chunk in stream:
            if 'response' in chunk:
                yield chunk['response']

    def get_all_qa_pairs(self) -> List[Dict[str, Any]]:
        """获取缓存中所有的问答对及其ID"""
        print("正在从ChromaDB获取所有缓存数据...")
        results = self.collection.get(include=["metadatas"])
        ids = results.get('ids', [])
        metadatas = results.get('metadatas', [])

        # 从元数据中提取问答对，并包含ID
        return [
            {
                "id": ids[i],
                "question": metadata.get("original_question", "N/A"),
                "answer": metadata.get("original_answer", "N/A")
            }
            for i, metadata in enumerate(metadatas)
        ]

    def delete_qa_pair(self, item_id: str):
        """根据ID删除单个问答对"""
        print(f"正在从ChromaDB删除条目，ID: {item_id}")
        self.collection.delete(ids=[item_id])
        print("删除成功。")

    def clear_cache(self):
        """清空整个缓存集合"""
        print("正在清空ChromaDB缓存...")
        # 删除并重新创建集合是清空它的最直接方法
        self.chroma_client.delete_collection(name=COLLECTION_NAME)
        # 修正：在重新创建时必须再次指定元数据，以确保使用正确的距离算法
        self.collection = self.chroma_client.get_or_create_collection(
            name=COLLECTION_NAME,
            metadata={"hnsw:space": "cosine"}
        )
        print("缓存已成功清空。")

    def get_distance_metric(self) -> str:
        """获取当前集合使用的距离度量标准"""
        try:
            # `hnsw:space` is stored in the collection's metadata
            return self.collection.metadata.get("hnsw:space", "未知")
        except Exception:
            return "无法获取"

# 创建一个全局的缓存服务实例
qa_cache_service = QACacheService() 