version: '3.8'

services:
  api:
    build: 
      context: .
      dockerfile: Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=0
    container_name: ai-bot-api
    environment:
      - CHROMA_TELEMETRY_ENABLED=FALSE
    restart: always
    ports:
      - "3000:3000"
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/mdm_chat/token/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M 