import httpx
from typing import Dict, Any, Optional, AsyncGenerator
import time

from cache import token_cache


class ExternalAPI:
    def __init__(self, base_url: str):
        self.base_url = base_url
        # 写死的账号密码
        self.account = "admin"
        self.password = "21232f297a57a5a743894a0e4a801fc3"  # admin的MD5值
        # 设置超时时间（秒）
        self.timeout = 100
        self.client = httpx.AsyncClient(timeout=self.timeout)
    
    async def login(self) -> Optional[Dict[str, Any]]:
        """
        调用外部登录API，使用写死的账号密码
        """
        url = f"{self.base_url}/api/api/v1/user/login"
        
        headers = {
            'Authorization': '',
            'Cookie': 'x-hng=lang=zh-CN',
            'Content-Type': 'application/json'
        }
        
        data = {
            "account": self.account,
            "password": self.password
        }
        
        # 最多重试3次
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                print(f"尝试登录 (第{retry_count+1}次)...")
                response = await self.client.post(url, headers=headers, json=data)
                response.raise_for_status()
                
                result = response.json()
                if result.get("code") == 200:
                    print("登录成功!")
                    return result.get("data")
                else:
                    print(f"登录失败: {result.get('msg')}")
                    return None
                    
            except httpx.TimeoutException:
                print(f"登录请求超时 (第{retry_count+1}次)")
                retry_count += 1
                if retry_count < max_retries:
                    # 等待一段时间后重试
                    time.sleep(2)
                else:
                    print("登录请求多次超时，放弃尝试")
                    return None
                    
            except httpx.ConnectError:
                print(f"连接错误 (第{retry_count+1}次)")
                retry_count += 1
                if retry_count < max_retries:
                    # 等待一段时间后重试
                    time.sleep(2)
                else:
                    print("连接错误多次，放弃尝试")
                    return None
                    
            except Exception as e:
                print(f"API调用错误: {str(e)}")
                return None
    
    async def chat(self, question: str, token: Optional[str] = None, stream: bool = False) -> Dict[str, Any]:
        """
        调用非流式对话接口，如果token无效会自动重新登录
        """
        url = f"{self.base_url}/api/api/v1/chat/global/infer"
        
        headers = {
            'Authorization': token if token else '',
            'Cookie': 'x-hng=lang=zh-CN',
            'Content-Type': 'application/json'
        }
        
        data = {
            "question": question,
            "qaType": "personal",
            "stream": stream
        }
        
        try:
            response = await self.client.post(url, headers=headers, json=data)
            
            # 如果返回401，说明token无效，需要重新登录
            if response.status_code == 401:
                print("Token无效，清除缓存并尝试重新登录...")
                token_cache.remove_token(self.account)
                new_token_data = await self.login()
                if new_token_data:
                    # 使用新token重试请求
                    headers['Authorization'] = new_token_data["token"]
                    response = await self.client.post(url, headers=headers, json=data)
                    return {
                        "success": True,
                        "data": response.json(),
                        "token_updated": True,
                        "new_token": new_token_data
                    }
                else:
                    return {
                        "success": False,
                        "error": "重新登录失败",
                        "token_updated": False
                    }
            
            response.raise_for_status()
            return {
                "success": True,
                "data": response.json(),
                "token_updated": False
            }
                
        except httpx.TimeoutException:
            print("对话请求超时")
            return {
                "success": False,
                "error": "请求超时",
                "token_updated": False
            }
                
        except httpx.ConnectError:
            print("对话连接错误")
            return {
                "success": False,
                "error": "连接错误",
                "token_updated": False
            }
                
        except Exception as e:
            print(f"对话API调用错误: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "token_updated": False
            }
    
    async def chat_stream(self, question: str, token: Optional[str] = None) -> AsyncGenerator:
        """
        调用流式对话接口，如果token无效会自动重新登录
        """
        url = f"{self.base_url}/api/api/v1/chat/global/infer"
        
        headers = {
            'Authorization': token if token else '',
            'Cookie': 'x-hng=lang=zh-CN',
            'Content-Type': 'application/json'
        }
        
        data = {
            "question": question,
            "qaType": "personal",
            "stream": True
        }
        
        try:
            async with self.client.stream("POST", url, headers=headers, json=data) as response:
                # 如果返回401，说明token无效，需要重新登录
                if response.status_code == 401:
                    print("Token无效，清除缓存并尝试重新登录...")
                    token_cache.remove_token(self.account)
                    new_token_data = await self.login()
                    if new_token_data:
                        # 使用新token重试请求
                        headers['Authorization'] = new_token_data["token"]
                        async with self.client.stream("POST", url, headers=headers, json=data) as retry_response:
                            # 返回新token信息
                            yield {
                                "type": "token_updated",
                                "token": new_token_data
                            }
                            retry_response.raise_for_status()
                            async for line in retry_response.aiter_lines():
                                if line:
                                    yield { "type": "data", "data": line }
                    else:
                        yield {
                            "type": "error",
                            "error": "重新登录失败"
                        }
                        return
                else:
                    response.raise_for_status()
                    # 流式返回数据
                    async for line in response.aiter_lines():
                        if line:
                            yield {
                                "type": "data",
                                "data": line
                            }
                    
        except httpx.TimeoutException:
            print("流式对话请求超时")
            yield {
                "type": "error",
                "error": "请求超时"
            }
                    
        except httpx.ConnectError:
            print("流式对话连接错误")
            yield {
                "type": "error",
                "error": "连接错误"
            }
                    
        except Exception as e:
            print(f"流式对话API调用错误: {str(e)}")
            yield {
                "type": "error",
                "error": str(e)
            }

# 创建外部API实例
external_api = ExternalAPI("http://150.5.172.124") 