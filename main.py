from fastapi import FastAP<PERSON>, HTTPEx<PERSON>, status, Header, BackgroundTasks, Request, Depends, APIRouter
from fastapi.responses import StreamingResponse, JSONResponse, HTMLResponse, RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import API<PERSON><PERSON>Header, HTTPBasic, HTTPBasicCredentials
from pydantic import BaseModel
from typing import Optional, Dict, Any, List, AsyncGenerator
import asyncio
import json
import requests
import time
import uuid
import httpx
from contextlib import asynccontextmanager
import ollama # 引入官方库
import secrets # 引入 secrets 用于安全比较

from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from cache import token_cache
from external_api import external_api
from cache_service import qa_cache_service, SIMILARITY_THRESHOLD, OLLAMA_HOST # 导入 OLLAMA_HOST

# --- 安全性配置 ---
API_KEY_HEADER = APIKeyHeader(name="X-API-Key")
VALID_API_KEYS = {
    "e8b1c4a9a0f8d3e2c6b5a7f6d5e4c3b2a1f0d9c8b7a6e5d4c3b2a1f0d9c8b7a6"
}

async def verify_api_key(api_key: str = Depends(API_KEY_HEADER)):
    """依赖项：验证 API 密钥"""
    if api_key not in VALID_API_KEYS:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing API Key",
            headers={"WWW-Authenticate": "Header"},
        )
    return api_key

# --- 新增: 仪表盘页面的基本认证 ---
security = HTTPBasic()

def authenticate_dashboard_user(credentials: HTTPBasicCredentials = Depends(security)):
    """依赖项：验证仪表盘用户的账号密码"""
    # 使用 secrets.compare_digest 来防止时序攻击，增加安全性
    correct_username = secrets.compare_digest(credentials.username, "zhafan")
    correct_password = secrets.compare_digest(credentials.password, "zhafan@246801?A")
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username

# --- 限流器配置 ---
limiter = Limiter(key_func=get_remote_address)

# --- 应用生命周期管理 ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 应用启动时执行
    print("应用启动...")
    # 创建一个全局的 httpx 客户端
    app.state.httpx_client = httpx.AsyncClient(timeout=100)
    # 检查Ollama模型
    await qa_cache_service._check_ollama_models()
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
    yield
    # 应用关闭时执行
    print("应用关闭...")
    await app.state.httpx_client.aclose()

app = FastAPI(
    title="FastAPI Chat Service",
    description="一个基于FastAPI的聊天服务",
    version="1.0.0",
    swagger_ui_parameters={"defaultModelsExpandDepth": -1, "docExpansion": "none"},
    lifespan=lifespan
)

# 创建一个APIRouter实例
router = APIRouter()

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

class ChatRequest(BaseModel):
    question: str

class ChatResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    token_updated: bool = False

class FastGPTMessage(BaseModel):
    role: str
    content: str

class FastGPTRequest(BaseModel):
    messages: List[FastGPTMessage]
    variables: Optional[Dict[str, Any]] = None

# 问题分类器 function schema
function_schema = {
    "name": "classify_and_respond_if_irrelevant",
    "description": "判断问题是否与'Easycontrol MDM'产品相关。如果无关，则生成一句礼貌的拒绝回复。",
    "parameters": {
        "type": "object",
        "properties": {
            "is_mdm_related": {
                "type": "boolean",
                "description": "如果问题与'Easycontrol MDM'产品功能、使用、价格、技术支持等相关，则为 true，否则为 false。"
            },
            "reason": {
                "type": "string",
                "description": "简要说明判断理由，例如：'问题是关于手机策略，属于MDM功能' 或 '问题是关于天气，与MDM无关'。"
            },
            "refusal_message": {
                "type": "string",
                "description": "当且仅当 'is_mdm_related' 为 false 时，生成一句礼貌、友好且专业的回复，告知用户当前问题超出服务范围，并引导其提出与'Easycontrol MDM'相关的问题。如果问题相关，此字段必须为空字符串。"
            }
        },
        "required": ["is_mdm_related", "reason", "refusal_message"]
    }
}


async def classify_question_mdm_related(question: str) -> dict:
    """
    【新策略】第一步：使用严格的JSON模式专注分类。
    """
    client = ollama.AsyncClient(host=OLLAMA_HOST)
    system_prompt = f"""你是一个智能的问题分类助手。你的唯一任务是判断用户问题是否与'Easycontrol MDM'产品相关，并返回一个严格的JSON对象作为分析结果。
你的回复必须是且仅是一个JSON对象，不含任何额外文本。
JSON结构: {{ "is_mdm_related": <boolean>, "reason": "<string>" }}
- "is_mdm_related": 如果问题关于'Easycontrol MDM'的功能、价格、支持或用法，则为true。否则为false。
- "reason": 对你的分类给出一句简短的解释。

用户问题: "{question}"
"""
    try:
        response = await client.generate(
            model='deepseek-v2:latest',
            system=system_prompt,
            prompt=" ", # Prompt可以留空，因为所有信息都在system prompt里
            format='json',
            stream=False
        )
        # response['response'] 是一个JSON字符串
        result = json.loads(response['response'])
        if "is_mdm_related" in result and "reason" in result:
            return result
        else:
            print("警告: 分类器返回的JSON格式不完整，默认相关。")
            return {"is_mdm_related": True, "reason": "JSON from model was incomplete."}
    except Exception as e:
        print(f"分类器调用异常: {e}. 默认相关。")
        return {"is_mdm_related": True, "reason": f"Classifier exception: {e}"}

async def generate_refusal_message_stream() -> AsyncGenerator[str, None]:
    """
    【新策略】第二步：如果问题无关，直接以此函数流式生成礼貌的拒绝消息。
    返回一个完整的、符合FastGPT格式的SSE事件流。
    """
    client = ollama.AsyncClient(host=OLLAMA_HOST)
    prompt = "你是一位'Easycontrol MDM'产品的客服。用户刚才提出了一个与产品无关的问题。请你用中文生成一句简短、礼貌的回复，告诉用户你只能解答与'Easycontrol MDM'相关的问题，并引导他提出相关问题。"
    
    # 1. 发送FastGPT兼容的初始块
    initial_chunk = {
        "id": f"chatcmpl-refusal-{uuid.uuid4()}", "object": "chat.completion.chunk", "created": int(time.time()),
        "choices": [{"delta": {"role": "assistant", "content": ""}, "index": 0, "finish_reason": None}]
    }
    yield f"data: {json.dumps(initial_chunk)}\n\n"

    try:
        # 2. 调用Ollama的流式生成接口
        stream = await client.generate(
            model='deepseek-v2:latest',
            prompt=prompt,
            stream=True
        )
        
        # 3. 迭代Ollama的响应，并包装成FastGPT格式
        async for chunk in stream:
            if chunk.get('done') is False:
                content_part = chunk.get('response', '')
                if content_part:
                    response_chunk = {
                        "id": f"chatcmpl-refusal-{uuid.uuid4()}", "object": "chat.completion.chunk", "created": int(time.time()),
                        "choices": [{"delta": {"content": content_part}, "index": 0, "finish_reason": None}]
                    }
                    yield f"data: {json.dumps(response_chunk)}\n\n"
    except Exception as e:
        print(f"生成拒绝消息流时出错: {e}")
        error_message = "抱歉，处理您的请求时遇到问题，请稍后再试。"
        error_chunk = {
            "id": f"chatcmpl-refusal-{uuid.uuid4()}", "object": "chat.completion.chunk", "created": int(time.time()),
            "choices": [{"delta": {"content": error_message}, "index": 0, "finish_reason": None}]
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"
    
    # 4. 发送FastGPT兼容的结束块
    end_chunk = {
        "id": f"chatcmpl-refusal-{uuid.uuid4()}", "object": "chat.completion.chunk", "created": int(time.time()),
        "choices": [{"delta": {}, "index": 0, "finish_reason": "stop"}]
    }
    yield f"data: {json.dumps(end_chunk)}\n\n"
    yield "data: [DONE]\n\n"


@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest, api_key: str = Depends(verify_api_key)):
    """
    非流式对话接口，自动处理token
    """
    try:
        # 从缓存中获取token
        user_data = token_cache.get_token(external_api.account)
        token = user_data.get("token") if user_data else None
        
        # 调用对话API
        result = await external_api.chat(request.question, token, stream=False)
        
        # 如果token被更新，更新缓存
        if result.get("token_updated") and result.get("new_token"):
            token_cache.set_token(external_api.account, result["new_token"])
        
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "token_updated": False
        }

@router.post("/chat/stream")
async def chat_stream(request: ChatRequest, api_key: str = Depends(verify_api_key)):
    """
    流式对话接口，自动处理token
    """
    try:
        # 从缓存中获取token
        user_data = token_cache.get_token(external_api.account)
        token = user_data.get("token") if user_data else None
        
        async def generate():
            print("开始流式响应...")  # 调试信息
            async for chunk in external_api.chat_stream(request.question, token):
                if chunk["type"] == "token_updated":
                    # 更新缓存中的token
                    token_cache.set_token(external_api.account, chunk["token"])
                    data = f"data: {chunk}\n\n"
                    print(f"发送token更新: {data}")  # 调试信息
                    yield data
                elif chunk["type"] == "error":
                    data = f"data: {chunk}\n\n"
                    print(f"发送错误: {data}")  # 调试信息
                    yield data
                else:
                    data = f"data: {chunk}\n\n"
                    print(f"发送数据块: {data}")  # 调试信息
                    yield data
                # 添加小延迟确保数据能够实时传输
                await asyncio.sleep(0.01)
        
        return StreamingResponse(
            generate(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"  # 禁用Nginx缓冲
            }
        )
    except Exception as e:
        print(f"流式响应错误: {str(e)}")  # 调试信息
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": str(e)
            }
        )

# appId=685a063e2cb5ce73bf1b6bc3
# key=fastgpt-isvJlYCSczSqL8kipsjXRceGIBdsnnc6MZKKnYyCSlCooB07hyuVbWDfG8A0

# fastgpt-tnBXv7IHHGpJkHPdIrsDveVHCIJb3KWuE3iIOKUPgnderRcdaByPlZqyNB1lf
FASTGPT_API_KEY = "fastgpt-oTP42xLU7GUhLg29s3yifwBh4d9FIMP7v2immngL1iyg5jUupwGsCuLwIPM9u"
FASTGPT_API_URL = "https://api.fastgpt.in/api/v1/chat/completions"

OLLAMA_HOST = "http://**************:11434" 

@router.post("/chat/fastgpt")
@limiter.limit("5/minute")
async def chat_fastgpt(
    request: Request,
    request_data: FastGPTRequest,
    background_tasks: BackgroundTasks,
    session_id: Optional[str] = Header(None, alias="X-Session-ID"),
    api_key: str = Depends(verify_api_key)
):
    """
    代理到 FastGPT 流式对话接口，并增加了智能问答缓存和前置分类。
    流程: 1. 查缓存 -> 2. (未命中) -> 分类问题 -> 3. (相关) -> FastGPT
    """
    if not request_data.messages:
        raise HTTPException(status_code=400, detail="messages 列表不能为空")
    
    user_question = request_data.messages[-1].content
    
    # 步骤 1: 尝试从缓存中查找答案
    cached_result = await qa_cache_service.search_similar_question(user_question)
    
    # 步骤 2: 检查缓存是否命中
    if cached_result and cached_result["similarity"] >= SIMILARITY_THRESHOLD:
        print("缓存命中！")
        # 记录分析信息
        global last_query_info
        last_query_info = {
            "query": user_question,
            "status": "命中",
            "closest_question": cached_result["question"],
            "similarity": cached_result["similarity"]
        }
        
        # 使用Ollama总结并返回
        cached_qa = {"question": cached_result["question"], "answer": cached_result["answer"]}
        async def ollama_summary_stream():
            """将Ollama的总结包装成FastGPT兼容的流式响应"""
            initial_chunk = {
                "id": f"chatcmpl-ollama-{uuid.uuid4()}", "object": "chat.completion.chunk", "created": int(time.time()),
                "choices": [{"delta": {"role": "assistant", "content": ""}, "index": 0, "finish_reason": None}]
            }
            yield f"data: {json.dumps(initial_chunk)}\n\n"
            
            async for content_part in qa_cache_service.summarize_answer_with_ollama(user_question, cached_qa):
                chunk = {
                    "id": f"chatcmpl-ollama-{uuid.uuid4()}", "object": "chat.completion.chunk", "created": int(time.time()),
                    "choices": [{"delta": {"content": content_part}, "index": 0, "finish_reason": None}]
                }
                yield f"data: {json.dumps(chunk)}\n\n"
            
            end_chunk = {
                "id": f"chatcmpl-ollama-{uuid.uuid4()}", "object": "chat.completion.chunk", "created": int(time.time()),
                "choices": [{"delta": {}, "index": 0, "finish_reason": "stop"}]
            }
            yield f"data: {json.dumps(end_chunk)}\n\n"
            yield "data: [DONE]\n\n"

        return StreamingResponse(ollama_summary_stream(), media_type="text/event-stream")

    # 步骤 2: 如果缓存未命中，进行问题分类
    print("缓存未命中，进行问题分类...")
    classification = await classify_question_mdm_related(user_question)
    is_mdm_related = classification.get("is_mdm_related")

    if not is_mdm_related:
        print(f"问题与MDM无关 (原因: {classification.get('reason')})。正在流式生成拒绝消息...")
        return StreamingResponse(generate_refusal_message_stream(), media_type="text/event-stream")

    # 步骤 3: 如果与MDM相关，则调用FastGPT
    print("问题与MDM相关，正在调用 FastGPT...")
    # 记录分析信息
    last_query_info = {
        "query": user_question,
        "status": "未命中 (但与MDM相关)",
        "closest_question": cached_result.get("question") if cached_result else "无相似问题",
        "similarity": cached_result.get("similarity") if cached_result else 0.0
    }
    
    headers = {
        'Authorization': f'Bearer {FASTGPT_API_KEY}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        "chatId": session_id,
        "stream": True,
        "detail": False,
        "messages": [msg.dict() for msg in request_data.messages],
    }
    if request_data.variables:
        payload["variables"] = request_data.variables

    async def stream_and_cache_generator():
        """
        这个生成器现在管理从连接到缓存的整个生命周期，以解决StreamClosed错误。
        """
        full_answer = ""
        try:
            # 1. 在生成器内部建立连接
            client = request.app.state.httpx_client
            async with client.stream("POST", FASTGPT_API_URL, headers=headers, json=payload) as response:
                response.raise_for_status()
                
                # 2. 迭代并将数据块实时转发给客户端
                async for chunk in response.aiter_bytes():
                    yield chunk
                    # 3. 在后台悄悄拼接完整答案以备缓存
                    try:
                        line = chunk.decode('utf-8').strip()
                        if line.startswith("data:"):
                            content = line[5:].strip()
                            if content != "[DONE]":
                                data = json.loads(content)
                                if data['choices'][0]['delta'].get('content'):
                                    full_answer += data['choices'][0]['delta']['content']
                    except Exception:
                        pass # 忽略解析错误，继续转发
                        
        except httpx.RequestError as e:
            # 如果流式传输本身失败，可以产生一个错误信息块
            print(f"Error while streaming from FastGPT: {e}")
            error_chunk = {"type": "error", "error": f"调用FastGPT API失败: {str(e)}"}
            yield f"data: {json.dumps(error_chunk)}\\n\\n"
        finally:
            # 4. 流结束后（无论成功与否），将最终拼接好的答案提交给后台缓存任务
            if full_answer:
                print(f"流结束，将答案缓存: {full_answer[:50]}...")
                background_tasks.add_task(qa_cache_service.add_qa_pair, user_question, full_answer)

    return StreamingResponse(stream_and_cache_generator(), media_type="text/event-stream")

@router.post("/chat/stream/mock")
async def chat_stream_mock(request: ChatRequest):
    """
    模拟流式对话接口，用于前端开发和测试。
    它不依赖外部API，并以与真实接口相同的数据格式返回一个模拟的流式响应。
    """
    async def generate_mock():
        """模拟生成流式响应"""
        mock_sentence = f"你好，关于 '{request.question}' 的问题，我是一个模拟AI助手。我将逐字为您生成一段模拟的回复。"
        # 将句子分割成小块，以模拟流式效果
        sentence_parts = [mock_sentence[i:i+2] for i in range(0, len(mock_sentence), 2)]

        for part in sentence_parts:
            # 1. 模拟最内层的数据负载 (与外部API返回的格式一致)
            inner_payload = {
                "id": f"chatcmpl-mock-{uuid.uuid4()}",
                "object": "chat.completion.chunk",
                "created": int(time.time()),
                "model": "mock-model",
                "choices": [{
                    "index": 0,
                    "delta": {"content": part},
                    "finish_reason": None
                }]
            }
            # 2. 模拟iter_lines()返回的原始行 'data: {...}'
            chunk_data_string = f"data: {json.dumps(inner_payload)}"

            # 3. 模拟external_api.py中chat_stream生成器返回的chunk
            main_chunk = {
                "type": "data",
                "data": chunk_data_string
            }

            # 4. 格式化为最终的SSE事件并发送
            yield f"data: {json.dumps(main_chunk)}\n\n"
            await asyncio.sleep(0.1) # 模拟网络和处理延迟

        # 发送标志着流结束的最终数据块
        final_inner_payload = {
            "id": f"chatcmpl-mock-{uuid.uuid4()}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": "mock-model",
            "choices": [{
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }]
        }
        final_chunk_data_string = f"data: {json.dumps(final_inner_payload)}"
        final_main_chunk = {"type": "data", "data": final_chunk_data_string}
        yield f"data: {json.dumps(final_main_chunk)}\n\n"

    return StreamingResponse(
        generate_mock(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )

@router.get("/token/status")
async def get_token_status():
    """
    获取当前token的状态和过期时间
    """
    try:
        user_data = token_cache.get_token(external_api.account)
        if user_data:
            return {
                "has_token": True,
                "account": external_api.account,
                "token": user_data.get("token")
            }
        else:
            return {
                "has_token": False,
                "account": external_api.account
            }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": str(e)
            }
        )

# 用于在内存中临时存储上一次查询的信息，以便在仪表盘上显示
last_query_info = {}

@router.get("/cache/dashboard", response_class=HTMLResponse)
async def cache_dashboard(username: str = Depends(authenticate_dashboard_user)):
    """
    显示一个包含所有缓存QA对的仪表盘，并提供删除和清空功能。
    此接口受HTTP基本认证保护。
    """
    qa_pairs = qa_cache_service.get_all_qa_pairs()
    distance_metric = qa_cache_service.get_distance_metric()
    
    # 构建上次查询的分析报告HTML
    analysis_html = ""
    if last_query_info:
        lq = last_query_info
        status_color = "green" if lq['status'] == "命中" else "orange"
        analysis_html = f"""
        <div class="analysis-box">
            <h3>上次查询分析</h3>
            <p><b>查询问题:</b> {lq['query']}</p>
            <p><b>缓存状态:</b> <span style="color:{status_color}; font-weight:bold;">{lq['status']}</span></p>
            <p><b>最相似问题 (通过重排):</b> {lq.get('closest_question', 'N/A')}</p>
            <p><b>重排分数:</b> {lq.get('similarity', 0.0):.4f} / <b>阈值:</b> {SIMILARITY_THRESHOLD}</p>
        </div>
        """
    
    html_content = f"""
    <html>
        <head>
            <title>问答缓存管理</title>
            <style>
                body {{ font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; margin: 0; background-color: #f7f9fc; color: #333; }}
                .container {{ max-width: 1000px; margin: 40px auto; padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 2px_10px rgba(0,0,0,0.05); }}
                h1, h3 {{ color: #007bff; border-bottom: 2px solid #eee; padding-bottom: 10px; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; table-layout: fixed; }}
                th, td {{ padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; word-wrap: break-word; }}
                th {{ background-color: #f2f2f2; }}
                tr:hover {{ background-color: #f5f5f5; }}
                .actions {{ margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center; }}
                .count {{ font-size: 1.1em; color: #555; }}
                .button-clear {{ background-color: #dc3545; }}
                .button-clear:hover {{ background-color: #c82333; }}
                .button-delete {{ background-color: #ffc107; font-size: 0.8em; padding: 5px 10px; }}
                .button-delete:hover {{ background-color: #e0a800; }}
                .button {{ color: white; padding: 10px 15px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; }}
                .empty-state, .analysis-box {{ text-align: center; padding: 20px; color: #777; background-color: #fafafa; border-radius: 5px; margin-top: 20px; }}
                .analysis-box p {{ margin: 5px 0; text-align: left; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="actions">
                    <h1>问答缓存管理</h1>
                    <form action="/mdm_chat/cache/clear" method="post" onsubmit="return confirm('确定要清空所有缓存吗？此操作不可恢复。');">
                        <button type="submit" class="button button-clear">清空全部缓存</button>
                    </form>
                </div>
                <div class="count">
                    <span>当前缓存数量: {len(qa_pairs)}</span>
                    <span style="margin-left: 20px;">距离算法: <strong style="color: #007bff;">{distance_metric}</strong></span>
                </div>
                
                {analysis_html}

                <table>
                    <thead>
                        <tr>
                            <th style="width: 40%;">问题 (Question)</th>
                            <th style="width: 45%;">答案 (Answer)</th>
                            <th style="width: 15%;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
    """
    
    if not qa_pairs:
        html_content += """
                        <tr>
                            <td colspan="3" class="empty-state">缓存为空</td>
                        </tr>
        """
    else:
        for pair in qa_pairs:
            html_content += f"""
                        <tr>
                            <td>{pair['question']}</td>
                            <td>{pair['answer']}</td>
                            <td>
                                <form action="/mdm_chat/cache/delete/{pair['id']}" method="post" onsubmit="return confirm('确定要删除这条缓存吗？');">
                                    <button type="submit" class="button button-delete">删除</button>
                                </form>
                            </td>
                        </tr>
            """
            
    html_content += """
                    </tbody>
                </table>
            </div>
        </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@router.post("/cache/clear")
async def clear_cache_endpoint():
    """
    清空所有QA缓存
    """
    qa_cache_service.clear_cache()
    return RedirectResponse(url="/mdm_chat/cache/dashboard", status_code=303)

@router.post("/cache/delete/{item_id}")
async def delete_cache_item_endpoint(item_id: str):
    """
    删除单条QA缓存
    """
    qa_cache_service.delete_qa_pair(item_id)
    return RedirectResponse(url="/mdm_chat/cache/dashboard", status_code=303)

# 将路由包含到主应用中，并添加统一前缀
app.include_router(router, prefix="/mdm_chat")

# 添加全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": str(exc)
        }
    ) 